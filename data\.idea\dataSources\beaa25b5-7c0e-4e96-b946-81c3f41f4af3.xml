<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="bookstore@localhost">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.53">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>|root||root|localhost|ALLOW_NONEXISTENT_DEFINER|G
|root||root|localhost|ALTER|G
|root||root|localhost|ALTER ROUTINE|G
|root||root|localhost|APPLICATION_PASSWORD_ADMIN|G
|root||mysql.infoschema|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.session|localhost|AUDIT_ABORT_EXEMPT|G
|root||mysql.sys|localhost|AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ABORT_EXEMPT|G
|root||root|localhost|AUDIT_ADMIN|G
|root||mysql.session|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||root|localhost|AUTHENTICATION_POLICY_ADMIN|G
|root||mysql.session|localhost|BACKUP_ADMIN|G
|root||root|localhost|BACKUP_ADMIN|G
|root||root|localhost|BINLOG_ADMIN|G
|root||root|localhost|BINLOG_ENCRYPTION_ADMIN|G
|root||mysql.session|localhost|CLONE_ADMIN|G
|root||root|localhost|CLONE_ADMIN|G
|root||mysql.session|localhost|CONNECTION_ADMIN|G
|root||root|localhost|CONNECTION_ADMIN|G
|root||root|localhost|CREATE|G
|root||root|localhost|CREATE ROLE|G
|root||root|localhost|CREATE ROUTINE|G
|root||root|localhost|CREATE TABLESPACE|G
|root||root|localhost|CREATE TEMPORARY TABLES|G
|root||root|localhost|CREATE USER|G
|root||root|localhost|CREATE VIEW|G
|root||root|localhost|DELETE|G
|root||root|localhost|DROP|G
|root||root|localhost|DROP ROLE|G
|root||root|localhost|ENCRYPTION_KEY_ADMIN|G
|root||root|localhost|EVENT|G
|root||root|localhost|EXECUTE|G
|root||root|localhost|FILE|G
|root||mysql.infoschema|localhost|FIREWALL_EXEMPT|G
|root||mysql.session|localhost|FIREWALL_EXEMPT|G
|root||mysql.sys|localhost|FIREWALL_EXEMPT|G
|root||root|localhost|FIREWALL_EXEMPT|G
|root||root|localhost|FLUSH_OPTIMIZER_COSTS|G
|root||root|localhost|FLUSH_PRIVILEGES|G
|root||root|localhost|FLUSH_STATUS|G
|root||root|localhost|FLUSH_TABLES|G
|root||root|localhost|FLUSH_USER_RESOURCES|G
|root||root|localhost|GROUP_REPLICATION_ADMIN|G
|root||root|localhost|GROUP_REPLICATION_STREAM|G
|root||root|localhost|INDEX|G
|root||root|localhost|INNODB_REDO_LOG_ARCHIVE|G
|root||root|localhost|INNODB_REDO_LOG_ENABLE|G
|root||root|localhost|INSERT|G
|root||root|localhost|LOCK TABLES|G
|root||root|localhost|OPTIMIZE_LOCAL_TABLE|G
|root||root|localhost|PASSWORDLESS_USER_ADMIN|G
|root||mysql.session|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PERSIST_RO_VARIABLES_ADMIN|G
|root||root|localhost|PROCESS|G
|root||root|localhost|REFERENCES|G
|root||root|localhost|RELOAD|G
|root||root|localhost|REPLICATION CLIENT|G
|root||root|localhost|REPLICATION SLAVE|G
|root||root|localhost|REPLICATION_APPLIER|G
|root||root|localhost|REPLICATION_SLAVE_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_ADMIN|G
|root||root|localhost|RESOURCE_GROUP_USER|G
|root||root|localhost|ROLE_ADMIN|G
|root||mysql.infoschema|localhost|SELECT|G
|root||root|localhost|SELECT|G
|root||root|localhost|SENSITIVE_VARIABLES_OBSERVER|G
|root||root|localhost|SERVICE_CONNECTION_ADMIN|G
|root||mysql.session|localhost|SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SESSION_VARIABLES_ADMIN|G
|root||root|localhost|SET_ANY_DEFINER|G
|root||root|localhost|SHOW DATABASES|G
|root||root|localhost|SHOW VIEW|G
|root||root|localhost|SHOW_ROUTINE|G
|root||mysql.session|localhost|SHUTDOWN|G
|root||root|localhost|SHUTDOWN|G
|root||mysql.session|localhost|SUPER|G
|root||root|localhost|SUPER|G
|root||mysql.infoschema|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_USER|G
|root||mysql.sys|localhost|SYSTEM_USER|G
|root||root|localhost|SYSTEM_USER|G
|root||mysql.session|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|SYSTEM_VARIABLES_ADMIN|G
|root||root|localhost|TABLE_ENCRYPTION_ADMIN|G
|root||root|localhost|TELEMETRY_LOG_ADMIN|G
|root||root|localhost|TRANSACTION_GTID_TAG|G
|root||root|localhost|TRIGGER|G
|root||root|localhost|UPDATE|G
|root||root|localhost|XA_RECOVER_ADMIN|G
|root||root|localhost|grant option|G
bookstore|schema||bookstore|localhost|ALTER|G
bookstore|schema||bookstore|localhost|ALTER ROUTINE|G
bookstore|schema||bookstore|localhost|CREATE|G
bookstore|schema||bookstore|localhost|CREATE ROUTINE|G
bookstore|schema||bookstore|localhost|CREATE TEMPORARY TABLES|G
bookstore|schema||bookstore|localhost|CREATE VIEW|G
bookstore|schema||bookstore|localhost|DELETE|G
bookstore|schema||bookstore|localhost|DROP|G
bookstore|schema||bookstore|localhost|EVENT|G
bookstore|schema||bookstore|localhost|EXECUTE|G
bookstore|schema||bookstore|localhost|INDEX|G
bookstore|schema||bookstore|localhost|INSERT|G
bookstore|schema||bookstore|localhost|LOCK TABLES|G
bookstore|schema||bookstore|localhost|REFERENCES|G
bookstore|schema||bookstore|localhost|SELECT|G
bookstore|schema||bookstore|localhost|SHOW VIEW|G
bookstore|schema||bookstore|localhost|TRIGGER|G
bookstore|schema||bookstore|localhost|UPDATE|G
collaborative_doc_db|schema||collab_user|localhost|ALTER|G
collaborative_doc_db|schema||collab_user|localhost|ALTER ROUTINE|G
collaborative_doc_db|schema||collab_user|localhost|CREATE|G
collaborative_doc_db|schema||collab_user|localhost|CREATE ROUTINE|G
collaborative_doc_db|schema||collab_user|localhost|CREATE TEMPORARY TABLES|G
collaborative_doc_db|schema||collab_user|localhost|CREATE VIEW|G
collaborative_doc_db|schema||collab_user|localhost|DELETE|G
collaborative_doc_db|schema||collab_user|localhost|DROP|G
collaborative_doc_db|schema||collab_user|localhost|EVENT|G
collaborative_doc_db|schema||collab_user|localhost|EXECUTE|G
collaborative_doc_db|schema||collab_user|localhost|INDEX|G
collaborative_doc_db|schema||collab_user|localhost|INSERT|G
collaborative_doc_db|schema||collab_user|localhost|LOCK TABLES|G
collaborative_doc_db|schema||collab_user|localhost|REFERENCES|G
collaborative_doc_db|schema||collab_user|localhost|SELECT|G
collaborative_doc_db|schema||collab_user|localhost|SHOW VIEW|G
collaborative_doc_db|schema||collab_user|localhost|TRIGGER|G
collaborative_doc_db|schema||collab_user|localhost|UPDATE|G
online_text_editor|schema||datagrip_user||ALTER|G
online_text_editor|schema||datagrip_user||ALTER ROUTINE|G
online_text_editor|schema||datagrip_user||CREATE|G
online_text_editor|schema||datagrip_user||CREATE ROUTINE|G
online_text_editor|schema||datagrip_user||CREATE TEMPORARY TABLES|G
online_text_editor|schema||datagrip_user||CREATE VIEW|G
online_text_editor|schema||datagrip_user||DELETE|G
online_text_editor|schema||datagrip_user||DROP|G
online_text_editor|schema||datagrip_user||EVENT|G
online_text_editor|schema||datagrip_user||EXECUTE|G
online_text_editor|schema||datagrip_user||INDEX|G
online_text_editor|schema||datagrip_user||INSERT|G
online_text_editor|schema||datagrip_user||LOCK TABLES|G
online_text_editor|schema||datagrip_user||REFERENCES|G
online_text_editor|schema||datagrip_user||SELECT|G
online_text_editor|schema||datagrip_user||SHOW VIEW|G
online_text_editor|schema||datagrip_user||TRIGGER|G
online_text_editor|schema||datagrip_user||UPDATE|G
online_text_editor|schema||datagrip_user||grant option|G
performance_schema|schema||mysql.session|localhost|SELECT|G
sys|schema||mysql.sys|localhost|TRIGGER|G</Grants>
      <ServerVersion>8.4.4</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="3" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="4" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="5" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="6" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="7" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="10" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="18" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="20" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="21" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="23" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="24" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="25" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="26" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="27" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="28" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="29" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="30" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="31" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="32" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="33" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="36" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="37" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="38" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="39" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="42" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="43" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="44" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="45" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="46" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="47" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="48" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="49" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="50" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="51" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="52" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="55" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="56" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="57" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="58" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="59" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="61" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="67" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="69" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="71" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="73" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="74" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="77" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="79" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="80" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="81" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="82" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="83" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="84" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="85" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="86" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="87" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="88" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="89" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="95" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="116" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="117" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="118" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="124" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="144" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="145" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="146" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="152" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="bookstore">
      <AutoIntrospectionLevel>3</AutoIntrospectionLevel>
      <Current>1</Current>
      <LastIntrospectionLevel>3</LastIntrospectionLevel>
      <LastIntrospectionLocalTimestamp>2025-07-04.02:48:04</LastIntrospectionLocalTimestamp>
      <CollationName>utf8mb4_unicode_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="collaborative_doc_db">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="291" parent="1" name="mysql">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="292" parent="1" name="online_text_editor">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="293" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="294" parent="1" name="sys">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <user id="295" parent="1" name="bookstore">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="296" parent="1" name="collab_user">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="297" parent="1" name="datagrip_user">
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="298" parent="1" name="mysql.infoschema">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="299" parent="1" name="mysql.session">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="300" parent="1" name="mysql.sys">
      <CanLogin>0</CanLogin>
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <user id="301" parent="1" name="root">
      <Host>localhost</Host>
      <Plugin>caching_sha2_password</Plugin>
    </user>
    <table id="302" parent="288" name="about">
      <Comment>网站介绍信息表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="303" parent="288" name="address">
      <Comment>收货地址表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="304" parent="288" name="announcement">
      <Comment>网站公告表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="305" parent="288" name="book">
      <Comment>图书表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="306" parent="288" name="book_comment">
      <Comment>图书评价表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="307" parent="288" name="bookimg">
      <Comment>图书图片表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="308" parent="288" name="bookorder">
      <Comment>订单表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="309" parent="288" name="booksort">
      <Comment>图书分类表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="310" parent="288" name="booksortlist">
      <Comment>图书分类关联表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="311" parent="288" name="cart">
      <Comment>购物车表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="312" parent="288" name="comment_like">
      <Comment>评论点赞记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="313" parent="288" name="coupon_template">
      <Comment>优惠券模板表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="314" parent="288" name="expense">
      <Comment>订单费用表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="288" name="newproduct">
      <Comment>新品推荐表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="316" parent="288" name="orderdetail">
      <Comment>订单明细表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="317" parent="288" name="publish">
      <Comment>出版社表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="318" parent="288" name="recommend">
      <Comment>推荐图书表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="319" parent="288" name="spike_activity">
      <Comment>秒杀活动表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="320" parent="288" name="spike_goods">
      <Comment>秒杀商品表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="321" parent="288" name="spike_record">
      <Comment>秒杀记录表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="322" parent="288" name="topic">
      <Comment>书单表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="323" parent="288" name="topic_fav">
      <Comment>书单收藏表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="324" parent="288" name="topic_item">
      <Comment>书单条目表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="325" parent="288" name="user">
      <Comment>用户表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="326" parent="288" name="user_coupon">
      <Comment>用户优惠券表</Comment>
      <DetailsLevel>3</DetailsLevel>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <view id="327" parent="288" name="user_available_coupons">
      <Definer>root@localhost</Definer>
      <DetailsLevel>3</DetailsLevel>
      <SourceTextLength>763</SourceTextLength>
    </view>
    <index id="328" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="329" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="330" parent="303" name="fk_address_user">
      <ColNames>account</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>account</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="331" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="332" parent="303" name="idx_account">
      <ColNames>account</ColNames>
      <Type>btree</Type>
    </index>
    <key id="333" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <index id="334" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="335" parent="304" name="idx_enable">
      <ColNames>enable</ColNames>
      <Type>btree</Type>
    </index>
    <key id="336" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <index id="337" parent="305" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="338" parent="305" name="uk_isbn">
      <ColNames>isbn</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="339" parent="305" name="idx_publish">
      <ColNames>publish</ColNames>
      <Type>btree</Type>
    </index>
    <index id="340" parent="305" name="idx_put">
      <ColNames>put</ColNames>
      <Type>btree</Type>
    </index>
    <index id="341" parent="305" name="idx_newProduct">
      <ColNames>newProduct</ColNames>
      <Type>btree</Type>
    </index>
    <index id="342" parent="305" name="idx_recommend">
      <ColNames>recommend</ColNames>
      <Type>btree</Type>
    </index>
    <key id="343" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="344" parent="305" name="uk_isbn">
      <UnderlyingIndexName>uk_isbn</UnderlyingIndexName>
    </key>
    <foreign-key id="345" parent="306" name="fk_comment_book">
      <ColNames>bookId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <foreign-key id="346" parent="306" name="fk_comment_user">
      <ColNames>userId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="347" parent="306" name="fk_comment_parent">
      <ColNames>parentId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book_comment</RefTableName>
    </foreign-key>
    <index id="348" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="349" parent="306" name="idx_bookId">
      <ColNames>bookId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="350" parent="306" name="idx_userId">
      <ColNames>userId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="351" parent="306" name="idx_parentId">
      <ColNames>parentId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="352" parent="306" name="idx_likeCount">
      <ColNames>likeCount</ColNames>
      <Type>btree</Type>
    </index>
    <index id="353" parent="306" name="idx_createTime">
      <ColNames>createTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="354" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="355" parent="307" name="fk_bookimg_book">
      <ColNames>isbn</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>isbn</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="356" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="357" parent="307" name="idx_isbn">
      <ColNames>isbn</ColNames>
      <Type>btree</Type>
    </index>
    <index id="358" parent="307" name="idx_cover">
      <ColNames>cover</ColNames>
      <Type>btree</Type>
    </index>
    <key id="359" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="360" parent="308" name="fk_bookorder_user">
      <ColNames>account</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>account</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="361" parent="308" name="fk_bookorder_address">
      <ColNames>addressId</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>address</RefTableName>
    </foreign-key>
    <index id="362" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="363" parent="308" name="uk_orderId">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="364" parent="308" name="idx_account">
      <ColNames>account</ColNames>
      <Type>btree</Type>
    </index>
    <index id="365" parent="308" name="fk_bookorder_address">
      <ColNames>addressId</ColNames>
      <Type>btree</Type>
    </index>
    <index id="366" parent="308" name="idx_orderStatus">
      <ColNames>orderStatus</ColNames>
      <Type>btree</Type>
    </index>
    <key id="367" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="368" parent="308" name="uk_orderId">
      <UnderlyingIndexName>uk_orderId</UnderlyingIndexName>
    </key>
    <index id="369" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="370" parent="309" name="idx_upperName">
      <ColNames>upperName</ColNames>
      <Type>btree</Type>
    </index>
    <index id="371" parent="309" name="idx_level">
      <ColNames>level</ColNames>
      <Type>btree</Type>
    </index>
    <key id="372" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="373" parent="310" name="fk_booksortlist_sort">
      <ColNames>bookSortId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>booksort</RefTableName>
    </foreign-key>
    <foreign-key id="374" parent="310" name="fk_booksortlist_book">
      <ColNames>bookId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="375" parent="310" name="PRIMARY">
      <ColNames>bookSortId
bookId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="376" parent="310" name="idx_bookId">
      <ColNames>bookId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="377" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="378" parent="311" name="fk_cart_user">
      <ColNames>account</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>account</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <foreign-key id="379" parent="311" name="fk_cart_book">
      <ColNames>id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="380" parent="311" name="PRIMARY">
      <ColNames>account
id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="381" parent="311" name="idx_account">
      <ColNames>account</ColNames>
      <Type>btree</Type>
    </index>
    <index id="382" parent="311" name="fk_cart_book">
      <ColNames>id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="383" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="384" parent="312" name="fk_like_comment">
      <ColNames>commentId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book_comment</RefTableName>
    </foreign-key>
    <foreign-key id="385" parent="312" name="fk_like_user">
      <ColNames>userId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="386" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="387" parent="312" name="uk_comment_user">
      <ColNames>commentId
userId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="388" parent="312" name="idx_userId">
      <ColNames>userId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="389" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="390" parent="312" name="uk_comment_user">
      <UnderlyingIndexName>uk_comment_user</UnderlyingIndexName>
    </key>
    <index id="391" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="392" parent="313" name="idx_type">
      <ColNames>type</ColNames>
      <Type>btree</Type>
    </index>
    <index id="393" parent="313" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="394" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="395" parent="314" name="fk_expense_order">
      <ColNames>orderId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>orderId</RefColNames>
      <RefTableName>bookorder</RefTableName>
    </foreign-key>
    <index id="396" parent="314" name="PRIMARY">
      <ColNames>orderId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="397" parent="314" name="idx_coupon_id">
      <ColNames>coupon_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="398" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="399" parent="315" name="fk_newproduct_book">
      <ColNames>bookId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="400" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="401" parent="315" name="uk_bookId">
      <ColNames>bookId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="402" parent="315" name="idx_rank">
      <ColNames>rank</ColNames>
      <Type>btree</Type>
    </index>
    <index id="403" parent="315" name="idx_addTime">
      <ColNames>addTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="404" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="405" parent="315" name="uk_bookId">
      <UnderlyingIndexName>uk_bookId</UnderlyingIndexName>
    </key>
    <foreign-key id="406" parent="316" name="fk_orderdetail_order">
      <ColNames>orderId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>orderId</RefColNames>
      <RefTableName>bookorder</RefTableName>
    </foreign-key>
    <foreign-key id="407" parent="316" name="fk_orderdetail_book">
      <ColNames>bookId</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="408" parent="316" name="PRIMARY">
      <ColNames>orderId
bookId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="409" parent="316" name="idx_bookId">
      <ColNames>bookId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="410" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <index id="411" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="412" parent="317" name="uk_name">
      <ColNames>name</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="413" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="414" parent="317" name="uk_name">
      <UnderlyingIndexName>uk_name</UnderlyingIndexName>
    </key>
    <foreign-key id="415" parent="318" name="fk_recommend_book">
      <ColNames>bookId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="416" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="417" parent="318" name="uk_bookId">
      <ColNames>bookId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="418" parent="318" name="idx_rank">
      <ColNames>rank</ColNames>
      <Type>btree</Type>
    </index>
    <index id="419" parent="318" name="idx_addTime">
      <ColNames>addTime</ColNames>
      <Type>btree</Type>
    </index>
    <key id="420" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="421" parent="318" name="uk_bookId">
      <UnderlyingIndexName>uk_bookId</UnderlyingIndexName>
    </key>
    <index id="422" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="423" parent="319" name="idx_activity_time_status">
      <ColNames>start_time
end_time
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="424" parent="319" name="idx_start_end_time">
      <ColNames>start_time
end_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="425" parent="319" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="426" parent="319" name="idx_created_by">
      <ColNames>created_by</ColNames>
      <Type>btree</Type>
    </index>
    <key id="427" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="428" parent="320" name="fk_spike_goods_activity">
      <ColNames>activity_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>spike_activity</RefTableName>
    </foreign-key>
    <foreign-key id="429" parent="320" name="fk_spike_goods_book">
      <ColNames>book_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="430" parent="320" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="431" parent="320" name="idx_goods_activity_status">
      <ColNames>activity_id
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="432" parent="320" name="idx_activity_id">
      <ColNames>activity_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="433" parent="320" name="idx_goods_book_status">
      <ColNames>book_id
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="434" parent="320" name="idx_book_id">
      <ColNames>book_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="435" parent="320" name="idx_sort_order">
      <ColNames>sort_order</ColNames>
      <Type>btree</Type>
    </index>
    <index id="436" parent="320" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="437" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="438" parent="321" name="fk_spike_record_goods">
      <ColNames>spike_goods_id</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>spike_goods</RefTableName>
    </foreign-key>
    <foreign-key id="439" parent="321" name="fk_spike_record_user">
      <ColNames>user_account</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>account</RefColNames>
      <RefTableName>user</RefTableName>
    </foreign-key>
    <index id="440" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="441" parent="321" name="idx_spike_goods_user">
      <ColNames>spike_goods_id
user_account</ColNames>
      <Type>btree</Type>
    </index>
    <index id="442" parent="321" name="idx_record_user_goods_result">
      <ColNames>user_account
spike_goods_id
result</ColNames>
      <Type>btree</Type>
    </index>
    <index id="443" parent="321" name="idx_user_account">
      <ColNames>user_account</ColNames>
      <Type>btree</Type>
    </index>
    <index id="444" parent="321" name="idx_spike_time">
      <ColNames>spike_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="445" parent="321" name="idx_result">
      <ColNames>result</ColNames>
      <Type>btree</Type>
    </index>
    <key id="446" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <index id="447" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="448" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <index id="449" parent="323" name="PRIMARY">
      <ColNames>userAccount
topicId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="450" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <foreign-key id="451" parent="324" name="fk_topic_item_topic">
      <ColNames>topicId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>topic</RefTableName>
    </foreign-key>
    <foreign-key id="452" parent="324" name="fk_topic_item_book">
      <ColNames>bookId</ColNames>
      <OnDelete>cascade</OnDelete>
      <RefColNames>id</RefColNames>
      <RefTableName>book</RefTableName>
    </foreign-key>
    <index id="453" parent="324" name="PRIMARY">
      <ColNames>topicId
bookId</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="454" parent="324" name="idx_bookId">
      <ColNames>bookId</ColNames>
      <Type>btree</Type>
    </index>
    <key id="455" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <index id="456" parent="325" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="457" parent="325" name="uk_account">
      <ColNames>account</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="458" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="459" parent="325" name="uk_account">
      <UnderlyingIndexName>uk_account</UnderlyingIndexName>
    </key>
    <foreign-key id="460" parent="326" name="fk_user_coupon_template">
      <ColNames>coupon_template_id</ColNames>
      <RefColNames>id</RefColNames>
      <RefTableName>coupon_template</RefTableName>
    </foreign-key>
    <index id="461" parent="326" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="462" parent="326" name="uk_coupon_code">
      <ColNames>coupon_code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="463" parent="326" name="idx_template_id">
      <ColNames>coupon_template_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="464" parent="326" name="idx_account_status">
      <ColNames>account
status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="465" parent="326" name="idx_account">
      <ColNames>account</ColNames>
      <Type>btree</Type>
    </index>
    <index id="466" parent="326" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="467" parent="326" name="idx_expire_time">
      <ColNames>expire_time</ColNames>
      <Type>btree</Type>
    </index>
    <key id="468" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="469" parent="326" name="uk_coupon_code">
      <UnderlyingIndexName>uk_coupon_code</UnderlyingIndexName>
    </key>
    <column id="470" parent="327" name="user_coupon_id">
      <Comment>用户优惠券ID</Comment>
      <NotNull>1</NotNull>
      <Position>1</Position>
      <StoredType>int|0s</StoredType>
    </column>
    <column id="471" parent="327" name="account">
      <Comment>用户账号</Comment>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="472" parent="327" name="coupon_code">
      <Comment>优惠券码（自动生成）</Comment>
      <NotNull>1</NotNull>
      <Position>3</Position>
      <StoredType>varchar(50)|0s</StoredType>
    </column>
    <column id="473" parent="327" name="coupon_name">
      <Comment>优惠券名称</Comment>
      <NotNull>1</NotNull>
      <Position>4</Position>
      <StoredType>varchar(100)|0s</StoredType>
    </column>
    <column id="474" parent="327" name="type">
      <Comment>优惠券类型：1-满减券，2-折扣券</Comment>
      <NotNull>1</NotNull>
      <Position>5</Position>
      <StoredType>tinyint(1)|0s</StoredType>
    </column>
    <column id="475" parent="327" name="discount_value">
      <Comment>折扣值（满减券为减免金额，折扣券为折扣百分比，如85表示8.5折）</Comment>
      <NotNull>1</NotNull>
      <Position>6</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="476" parent="327" name="min_order_amount">
      <Comment>最低消费金额</Comment>
      <Position>7</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="477" parent="327" name="max_discount_amount">
      <Comment>最大折扣金额（仅折扣券使用）</Comment>
      <Position>8</Position>
      <StoredType>decimal(10,2 digit)|0s</StoredType>
    </column>
    <column id="478" parent="327" name="receive_time">
      <Comment>领取时间</Comment>
      <Position>9</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="479" parent="327" name="expire_time">
      <Comment>过期时间</Comment>
      <NotNull>1</NotNull>
      <Position>10</Position>
      <StoredType>timestamp|0s</StoredType>
    </column>
    <column id="480" parent="327" name="discount_desc">
      <Position>11</Position>
      <StoredType>varchar(26)|0s</StoredType>
    </column>
  </database-model>
</dataSource>