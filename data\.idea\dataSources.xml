<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="DataSourceManagerImpl" format="xml" multifile-model="true">
    <data-source source="LOCAL" name="0@localhost" uuid="15bff07b-47ee-458d-9b52-0152715439db">
      <driver-ref>redis</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>jdbc.RedisDriver</jdbc-driver>
      <jdbc-url>*****************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
    <data-source source="LOCAL" name="bookstore@localhost" uuid="beaa25b5-7c0e-4e96-b946-81c3f41f4af3">
      <driver-ref>mysql.8</driver-ref>
      <synchronize>true</synchronize>
      <jdbc-driver>com.mysql.cj.jdbc.Driver</jdbc-driver>
      <jdbc-url>*************************************</jdbc-url>
      <working-dir>$ProjectFileDir$</working-dir>
    </data-source>
  </component>
</project>