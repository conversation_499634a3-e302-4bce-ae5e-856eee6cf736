-- ========================================
-- 书店系统数据库初始化脚本
-- 数据库名称: bookstore
-- 字符集: utf8mb4
-- 排序规则: utf8mb4_unicode_ci
-- ========================================
DROP DATABASE IF EXISTS `bookstore`;
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `bookstore`
    DEFAULT CHARACTER SET utf8mb4
    COLLATE utf8mb4_unicode_ci;

USE `bookstore`;

-- ========================================
-- 用户相关表
-- ========================================

-- 用户表
CREATE TABLE `user` (
                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户编号',
                        `account` varchar(100) NOT NULL COMMENT '用户账号(邮箱)',
                        `password` varchar(255) NOT NULL COMMENT '密码',
                        `name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
                        `gender` varchar(10) DEFAULT NULL COMMENT '性别',
                        `imgUrl` varchar(255) DEFAULT NULL COMMENT '头像URL',
                        `info` text COMMENT '个人简介',
                        `manage` tinyint(1) DEFAULT '0' COMMENT '是否为管理员',
                        `enable` tinyint(1) DEFAULT '1' COMMENT '是否启用',
                        `registerTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `uk_account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 地址表
CREATE TABLE `address` (
                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '地址编号',
                           `account` varchar(100) NOT NULL COMMENT '用户账号',
                           `name` varchar(50) NOT NULL COMMENT '收货人姓名',
                           `phone` varchar(20) NOT NULL COMMENT '收货人电话',
                           `addr` varchar(255) NOT NULL COMMENT '具体地址',
                           `label` varchar(50) DEFAULT NULL COMMENT '地址标签',
                           `off` tinyint(1) DEFAULT '0' COMMENT '是否删除',
                           PRIMARY KEY (`id`),
                           KEY `idx_account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收货地址表';

-- 购物车表
CREATE TABLE `cart` (
                        `account` varchar(100) NOT NULL COMMENT '用户账号',
                        `id` int(11) NOT NULL COMMENT '图书ID',
                        `num` int(11) DEFAULT '1' COMMENT '数量',
                        `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                        PRIMARY KEY (`account`, `id`),
                        KEY `idx_account` (`account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='购物车表';

-- ========================================
-- 图书相关表
-- ========================================

-- 图书表
CREATE TABLE `book` (
                        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '图书编号',
                        `bookName` varchar(255) NOT NULL COMMENT '图书名称',
                        `author` varchar(255) DEFAULT NULL COMMENT '作者',
                        `isbn` varchar(50) NOT NULL COMMENT 'ISBN号',
                        `publish` varchar(255) DEFAULT NULL COMMENT '出版社',
                        `birthday` timestamp NULL DEFAULT NULL COMMENT '出版时间',
                        `marketPrice` decimal(10,2) DEFAULT NULL COMMENT '市场价',
                        `price` decimal(10,2) DEFAULT NULL COMMENT '售价',
                        `stock` int(11) DEFAULT '0' COMMENT '库存',
                        `description` text COMMENT '图书描述',
                        `put` tinyint(1) DEFAULT '1' COMMENT '是否上架',
                        `rank` int(11) DEFAULT '0' COMMENT '权重值',
                        `newProduct` tinyint(1) DEFAULT '0' COMMENT '是否新品',
                        `recommend` tinyint(1) DEFAULT '0' COMMENT '是否推荐',
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `uk_isbn` (`isbn`),
                        KEY `idx_publish` (`publish`),
                        KEY `idx_put` (`put`),
                        KEY `idx_newProduct` (`newProduct`),
                        KEY `idx_recommend` (`recommend`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书表';

-- 图书图片表
CREATE TABLE `bookimg` (
                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '图片编号',
                           `isbn` varchar(50) NOT NULL COMMENT '图书ISBN',
                           `imgSrc` varchar(255) NOT NULL COMMENT '图片路径',
                           `cover` tinyint(1) DEFAULT '0' COMMENT '是否为封面',
                           PRIMARY KEY (`id`),
                           KEY `idx_isbn` (`isbn`),
                           KEY `idx_cover` (`cover`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书图片表';

-- 图书分类表
CREATE TABLE `booksort` (
                            `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类编号',
                            `sortName` varchar(100) NOT NULL COMMENT '分类名称',
                            `upperName` varchar(100) DEFAULT '无' COMMENT '上级分类名称',
                            `level` varchar(20) DEFAULT NULL COMMENT '分类级别',
                            `rank` int(11) DEFAULT '0' COMMENT '排序权重',
                            PRIMARY KEY (`id`),
                            KEY `idx_upperName` (`upperName`),
                            KEY `idx_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书分类表';

-- 图书分类关联表
CREATE TABLE `booksortlist` (
                                `bookSortId` int(11) NOT NULL COMMENT '分类ID',
                                `bookId` int(11) NOT NULL COMMENT '图书ID',
                                PRIMARY KEY (`bookSortId`, `bookId`),
                                KEY `idx_bookId` (`bookId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书分类关联表';

-- 出版社表
CREATE TABLE `publish` (
                           `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '出版社编号',
                           `name` varchar(255) NOT NULL COMMENT '出版社名称',
                           `showPublish` tinyint(1) DEFAULT '1' COMMENT '是否显示',
                           `rank` int(11) DEFAULT '0' COMMENT '排序权重',
                           `num` int(11) DEFAULT '0' COMMENT '图书数量',
                           PRIMARY KEY (`id`),
                           UNIQUE KEY `uk_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出版社表';

-- ========================================
-- 书单相关表
-- ========================================

-- 删除旧表，避免冲突
DROP TABLE IF EXISTS `booktopic`;
DROP TABLE IF EXISTS `subbooktopic`;

-- 新书单主题表（Topic）
CREATE TABLE `topic` (
                         `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '书单ID',
                         `title` varchar(255) NOT NULL COMMENT '书单标题',
                         `subTitle` varchar(255) DEFAULT NULL COMMENT '副标题',
                         `cover` varchar(1024) DEFAULT NULL COMMENT '封面图片',
                         `rank` int(11) DEFAULT 0 COMMENT '排序权重',
                         `status` tinyint(1) DEFAULT 1 COMMENT '是否上架(1=上架)',
                         `viewCnt` int(11) DEFAULT 0 COMMENT '浏览量',
                         `favCnt` int(11) DEFAULT 0 COMMENT '收藏量',
                         `orderCnt` int(11) DEFAULT 0 COMMENT '成交量',
                         `createdAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                         `updatedAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书单表';

-- 书单条目表（Topic Item）
CREATE TABLE `topic_item` (
                              `topicId` int(11) NOT NULL COMMENT '书单ID',
                              `bookId` int(11) NOT NULL COMMENT '图书ID',
                              `recommendReason` text COMMENT '推荐理由',
                              `orderNo` int(11) DEFAULT 0 COMMENT '条目排序',
                              PRIMARY KEY (`topicId`,`bookId`),
                              KEY `idx_bookId` (`bookId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书单条目表';

-- 书单收藏表（Topic Fav）
CREATE TABLE `topic_fav` (
                             `userAccount` varchar(100) NOT NULL COMMENT '用户账号',
                             `topicId` int(11) NOT NULL COMMENT '书单ID',
                             `favAt` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
                             PRIMARY KEY (`userAccount`,`topicId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书单收藏表';

-- ========================================
-- 订单相关表
-- ========================================

-- 订单表
CREATE TABLE `bookorder` (
                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单编号',
                             `orderId` varchar(50) NOT NULL COMMENT '订单号',
                             `account` varchar(100) NOT NULL COMMENT '用户账号',
                             `addressId` int(11) NOT NULL COMMENT '收货地址ID',
                             `orderTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '下单时间',
                             `shipTime` timestamp NULL DEFAULT NULL COMMENT '发货时间',
                             `getTime` timestamp NULL DEFAULT NULL COMMENT '收货时间',
                             `evaluateTime` timestamp NULL DEFAULT NULL COMMENT '评价时间',
                             `closeTime` timestamp NULL DEFAULT NULL COMMENT '关闭时间',
                             `confirmTime` timestamp NULL DEFAULT NULL COMMENT '确认收货时间',
                             `orderStatus` varchar(50) DEFAULT '待付款' COMMENT '订单状态',
                             `logisticsCompany` int(11) DEFAULT NULL COMMENT '物流公司ID',
                             `logisticsNum` varchar(100) DEFAULT NULL COMMENT '物流单号',
                             `beUserDelete` tinyint(1) DEFAULT '0' COMMENT '用户是否删除',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `uk_orderId` (`orderId`),
                             KEY `idx_account` (`account`),
                             KEY `idx_orderStatus` (`orderStatus`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单明细表
CREATE TABLE `orderdetail` (
                               `orderId` varchar(50) NOT NULL COMMENT '订单号',
                               `bookId` int(11) NOT NULL COMMENT '图书ID',
                               `num` int(11) NOT NULL COMMENT '购买数量',
                               `price` decimal(10,2) NOT NULL COMMENT '购买时单价',
                               PRIMARY KEY (`orderId`, `bookId`),
                               KEY `idx_bookId` (`bookId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单明细表';

-- 订单费用表
CREATE TABLE `expense` (
                           `orderId` varchar(50) NOT NULL COMMENT '订单号',
                           `productTotalMoney` decimal(10,2) DEFAULT '0.00' COMMENT '商品总价',
                           `freight` decimal(10,2) DEFAULT '0.00' COMMENT '运费',
                           `coupon` int(11) DEFAULT '0' COMMENT '优惠券',
                           `activityDiscount` decimal(10,2) DEFAULT '0.00' COMMENT '活动优惠',
                           `allPrice` decimal(10,2) DEFAULT '0.00' COMMENT '订单总金额',
                           `finallyPrice` decimal(10,2) DEFAULT '0.00' COMMENT '最终实付金额',
                           PRIMARY KEY (`orderId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单费用表';

-- ========================================
-- 创建缺失的数据库表
-- ========================================

USE `bookstore`;

-- 新品推荐表
CREATE TABLE `newproduct` (
                              `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '新品推荐编号',
                              `bookId` int(11) NOT NULL COMMENT '图书ID',
                              `rank` int(11) DEFAULT '0' COMMENT '推荐权重',
                              `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_bookId` (`bookId`),
                              KEY `idx_rank` (`rank`),
                              KEY `idx_addTime` (`addTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新品推荐表';

-- 推荐图书表
CREATE TABLE `recommend` (
                             `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '推荐编号',
                             `bookId` int(11) NOT NULL COMMENT '图书ID',
                             `rank` int(11) DEFAULT '0' COMMENT '推荐权重',
                             `addTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
                             PRIMARY KEY (`id`),
                             UNIQUE KEY `uk_bookId` (`bookId`),
                             KEY `idx_rank` (`rank`),
                             KEY `idx_addTime` (`addTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='推荐图书表';

-- 添加外键约束
ALTER TABLE `newproduct` ADD CONSTRAINT `fk_newproduct_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;
ALTER TABLE `recommend` ADD CONSTRAINT `fk_recommend_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;

-- 插入一些示例数据（如果book表中有数据的话）
-- 注意：这些INSERT语句只有在book表中存在对应ID的记录时才会成功

-- 检查是否有图书数据
SELECT COUNT(*) as book_count FROM book;

-- 如果有图书数据，可以添加一些推荐和新品
-- INSERT INTO `newproduct` (`bookId`, `rank`)
-- SELECT `id`, 1 FROM `book` WHERE `newProduct` = 1 LIMIT 10;

-- INSERT INTO `recommend` (`bookId`, `rank`)
-- SELECT `id`, 1 FROM `book` WHERE `recommend` = 1 LIMIT 10;


-- ========================================
-- 索引和外键约束
-- ========================================

-- 添加外键约束
ALTER TABLE `address` ADD CONSTRAINT `fk_address_user` FOREIGN KEY (`account`) REFERENCES `user` (`account`) ON DELETE CASCADE;
ALTER TABLE `cart` ADD CONSTRAINT `fk_cart_user` FOREIGN KEY (`account`) REFERENCES `user` (`account`) ON DELETE CASCADE;
ALTER TABLE `cart` ADD CONSTRAINT `fk_cart_book` FOREIGN KEY (`id`) REFERENCES `book` (`id`) ON DELETE CASCADE;
ALTER TABLE `bookimg` ADD CONSTRAINT `fk_bookimg_book` FOREIGN KEY (`isbn`) REFERENCES `book` (`isbn`) ON DELETE CASCADE;
ALTER TABLE `booksortlist` ADD CONSTRAINT `fk_booksortlist_sort` FOREIGN KEY (`bookSortId`) REFERENCES `booksort` (`id`) ON DELETE CASCADE;
ALTER TABLE `booksortlist` ADD CONSTRAINT `fk_booksortlist_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;
ALTER TABLE `topic_item` ADD CONSTRAINT `fk_topic_item_topic` FOREIGN KEY (`topicId`) REFERENCES `topic` (`id`) ON DELETE CASCADE;
ALTER TABLE `topic_item` ADD CONSTRAINT `fk_topic_item_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE;
ALTER TABLE `bookorder` ADD CONSTRAINT `fk_bookorder_user` FOREIGN KEY (`account`) REFERENCES `user` (`account`) ON DELETE CASCADE;
ALTER TABLE `bookorder` ADD CONSTRAINT `fk_bookorder_address` FOREIGN KEY (`addressId`) REFERENCES `address` (`id`);
ALTER TABLE `orderdetail` ADD CONSTRAINT `fk_orderdetail_order` FOREIGN KEY (`orderId`) REFERENCES `bookorder` (`orderId`) ON DELETE CASCADE;
ALTER TABLE `orderdetail` ADD CONSTRAINT `fk_orderdetail_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`);
ALTER TABLE `expense` ADD CONSTRAINT `fk_expense_order` FOREIGN KEY (`orderId`) REFERENCES `bookorder` (`orderId`) ON DELETE CASCADE;



-- ========================================
-- 秒杀系统表结构 - 适配现有bookstore系统
-- 执行前请确保已连接到bookstore数据库
-- ========================================

USE `bookstore`;

-- ========================================
-- 1. 秒杀活动表
-- ========================================
CREATE TABLE `spikeActivity` (
                                 `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动ID',
                                 `activityName` varchar(100) NOT NULL COMMENT '活动名称',
                                 `activityDesc` text COMMENT '活动描述',
                                 `startTime` datetime NOT NULL COMMENT '开始时间',
                                 `endTime` datetime NOT NULL COMMENT '结束时间',
                                 `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始，1-进行中，2-已结束，3-已取消',
                                 `createTime` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                 `updateTime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                 `createdBy` varchar(100) COMMENT '创建人账号',
                                 PRIMARY KEY (`id`),
                                 KEY `idx_startEndTime` (`startTime`, `endTime`),
                                 KEY `idx_status` (`status`),
                                 KEY `idx_createdBy` (`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀活动表';

-- ========================================
-- 2. 秒杀商品表
-- ========================================
CREATE TABLE `spikeGoods` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '秒杀商品ID',
                              `activityId` bigint(20) NOT NULL COMMENT '活动ID',
                              `bookId` int(11) NOT NULL COMMENT '图书ID',
                              `spikePrice` decimal(10,2) NOT NULL COMMENT '秒杀价格',
                              `originalPrice` decimal(10,2) NOT NULL COMMENT '原价',
                              `spikeStock` int(11) NOT NULL COMMENT '秒杀库存',
                              `soldCount` int(11) DEFAULT '0' COMMENT '已售数量',
                              `limitPerUser` int(11) DEFAULT '1' COMMENT '每人限购数量',
                              `sortOrder` int(11) DEFAULT '0' COMMENT '排序',
                              `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-下架，1-上架',
                              `createTime` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updateTime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              KEY `idx_activityId` (`activityId`),
                              KEY `idx_bookId` (`bookId`),
                              KEY `idx_status` (`status`),
                              KEY `idx_sortOrder` (`sortOrder`),
                              CONSTRAINT `fk_spikeGoods_activity` FOREIGN KEY (`activityId`) REFERENCES `spikeActivity` (`id`) ON DELETE CASCADE,
                              CONSTRAINT `fk_spikeGoods_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀商品表';

-- ========================================
-- 3. 秒杀订单表
-- ========================================
CREATE TABLE `spikeOrder` (
                              `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '秒杀订单ID',
                              `orderId` varchar(50) NOT NULL COMMENT '订单号',
                              `spikeGoodsId` bigint(20) NOT NULL COMMENT '秒杀商品ID',
                              `userAccount` varchar(100) NOT NULL COMMENT '用户账号',
                              `quantity` int(11) NOT NULL DEFAULT '1' COMMENT '购买数量',
                              `spikePrice` decimal(10,2) NOT NULL COMMENT '秒杀价格',
                              `totalAmount` decimal(10,2) NOT NULL COMMENT '总金额',
                              `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-待支付，1-已支付，2-已取消，3-已退款',
                              `payTime` datetime COMMENT '支付时间',
                              `cancelTime` datetime COMMENT '取消时间',
                              `expireTime` datetime NOT NULL COMMENT '过期时间',
                              `createTime` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                              `updateTime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                              PRIMARY KEY (`id`),
                              UNIQUE KEY `uk_orderId` (`orderId`),
                              KEY `idx_spikeGoodsId` (`spikeGoodsId`),
                              KEY `idx_userAccount` (`userAccount`),
                              KEY `idx_status` (`status`),
                              KEY `idx_expireTime` (`expireTime`),
                              KEY `idx_createTime` (`createTime`),
                              CONSTRAINT `fk_spikeOrder_goods` FOREIGN KEY (`spikeGoodsId`) REFERENCES `spikeGoods` (`id`) ON DELETE CASCADE,
                              CONSTRAINT `fk_spikeOrder_user` FOREIGN KEY (`userAccount`) REFERENCES `user` (`account`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀订单表';

-- ========================================
-- 4. 秒杀记录表
-- ========================================
CREATE TABLE `spikeRecord` (
                               `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
                               `spikeGoodsId` bigint(20) NOT NULL COMMENT '秒杀商品ID',
                               `userAccount` varchar(100) NOT NULL COMMENT '用户账号',
                               `spikeTime` datetime NOT NULL COMMENT '秒杀时间',
                               `result` tinyint(1) NOT NULL COMMENT '结果：0-失败，1-成功',
                               `failReason` varchar(200) COMMENT '失败原因',
                               `ipAddress` varchar(45) COMMENT 'IP地址',
                               `userAgent` varchar(500) COMMENT '用户代理',
                               PRIMARY KEY (`id`),
                               KEY `idx_spikeGoodsUser` (`spikeGoodsId`, `userAccount`),
                               KEY `idx_spikeTime` (`spikeTime`),
                               KEY `idx_result` (`result`),
                               KEY `idx_userAccount` (`userAccount`),
                               CONSTRAINT `fk_spikeRecord_goods` FOREIGN KEY (`spikeGoodsId`) REFERENCES `spikeGoods` (`id`) ON DELETE CASCADE,
                               CONSTRAINT `fk_spikeRecord_user` FOREIGN KEY (`userAccount`) REFERENCES `user` (`account`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='秒杀记录表';

-- ========================================
-- 5. 插入示例数据
-- ========================================

-- 插入秒杀活动
INSERT INTO `spikeActivity` (`activityName`, `activityDesc`, `startTime`, `endTime`, `status`, `createdBy`) VALUES
                                                                                                                ('新年特惠秒杀', '新年期间限时秒杀活动，精选图书超低价', '2024-01-01 10:00:00', '2024-01-01 12:00:00', 1, '<EMAIL>'),
                                                                                                                ('午间秒杀场', '午间休息时间秒杀专场', '2024-01-01 12:00:00', '2024-01-01 14:00:00', 0, '<EMAIL>'),
                                                                                                                ('晚间秒杀场', '晚间黄金时段秒杀活动', '2024-01-01 20:00:00', '2024-01-01 22:00:00', 0, '<EMAIL>');

-- 插入秒杀商品（基于现有的图书数据）
INSERT INTO `spikeGoods` (`activityId`, `bookId`, `spikePrice`, `originalPrice`, `spikeStock`, `limitPerUser`, `sortOrder`) VALUES
                                                                                                                                (1, 1, 19.90, 45.00, 50, 2, 1),  -- 红楼梦
                                                                                                                                (1, 2, 49.90, 98.00, 30, 1, 2),  -- Java核心技术
                                                                                                                                (1, 3, 69.90, 128.00, 20, 1, 3), -- 算法导论
                                                                                                                                (2, 4, 39.90, 69.00, 40, 2, 1),  -- Spring Boot实战
                                                                                                                                (2, 5, 15.90, 38.00, 60, 2, 2),  -- 西游记
                                                                                                                                (3, 1, 22.90, 45.00, 30, 1, 1),  -- 红楼梦（晚场）
                                                                                                                                (3, 2, 59.90, 98.00, 25, 1, 2);  -- Java核心技术（晚场）

-- ========================================
-- 6. 创建索引优化查询性能
-- ========================================

-- 秒杀活动表复合索引
CREATE INDEX `idx_activity_time_status` ON `spikeActivity` (`startTime`, `endTime`, `status`);

-- 秒杀商品表复合索引
CREATE INDEX `idx_goods_activity_status` ON `spikeGoods` (`activityId`, `status`);
CREATE INDEX `idx_goods_book_status` ON `spikeGoods` (`bookId`, `status`);

-- 秒杀订单表复合索引
CREATE INDEX `idx_order_user_time` ON `spikeOrder` (`userAccount`, `createTime`);
CREATE INDEX `idx_order_goods_status` ON `spikeOrder` (`spikeGoodsId`, `status`);

-- 秒杀记录表复合索引
CREATE INDEX `idx_record_user_goods` ON `spikeRecord` (`userAccount`, `spikeGoodsId`);
CREATE INDEX `idx_record_time_result` ON `spikeRecord` (`spikeTime`, `result`);

-- ========================================
-- 7. 验证表创建结果
-- ========================================

-- 查看创建的表
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '行数'
FROM INFORMATION_SCHEMA.TABLES
WHERE TABLE_SCHEMA = 'bookstore'
  AND TABLE_NAME LIKE 'spike%'
ORDER BY TABLE_NAME;

-- 查看外键约束
SELECT
    TABLE_NAME as '表名',
    COLUMN_NAME as '列名',
    CONSTRAINT_NAME as '约束名',
    REFERENCED_TABLE_NAME as '引用表',
    REFERENCED_COLUMN_NAME as '引用列'
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = 'bookstore'
  AND TABLE_NAME LIKE 'spike%'
  AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 查看示例数据
SELECT
    a.id,
    a.activityName,
    a.startTime,
    a.endTime,
    a.status,
    COUNT(g.id) as goodsCount
FROM spikeActivity a
         LEFT JOIN spikeGoods g ON a.id = g.activityId
GROUP BY a.id, a.activityName, a.startTime, a.endTime, a.status
ORDER BY a.startTime;

-- 查看秒杀商品详情
SELECT
    g.id,
    a.activityName,
    b.bookName,
    g.spikePrice,
    g.originalPrice,
    ROUND((g.spikePrice / g.originalPrice) * 10, 1) as discount,
    g.spikeStock,
    g.soldCount,
    g.status
FROM spikeGoods g
         JOIN spikeActivity a ON g.activityId = a.id
         JOIN book b ON g.bookId = b.id
ORDER BY g.activityId, g.sortOrder;

-- ========================================
-- 8. 数据完整性检查
-- ========================================

-- 检查是否有孤立的秒杀商品（引用不存在的图书）
SELECT 'spikeGoods中引用不存在的图书' as checkType, COUNT(*) as count
FROM spikeGoods g
         LEFT JOIN book b ON g.bookId = b.id
WHERE b.id IS NULL;

-- 检查是否有孤立的秒杀商品（引用不存在的活动）
SELECT 'spikeGoods中引用不存在的活动' as checkType, COUNT(*) as count
FROM spikeGoods g
         LEFT JOIN spikeActivity a ON g.activityId = a.id
WHERE a.id IS NULL;

-- 检查用户账号是否存在
SELECT 'spikeActivity中不存在的创建人' as checkType, COUNT(*) as count
FROM spikeActivity a
         LEFT JOIN user u ON a.createdBy = u.account
WHERE a.createdBy IS NOT NULL AND u.account IS NULL;

-- ========================================
-- 9. 性能优化建议
-- ========================================

/*
性能优化建议：

1. 数据库层面：
   - 已创建必要的索引优化查询性能
   - 使用InnoDB引擎支持事务和外键约束
   - 字段类型选择合理，避免浪费存储空间

2. 缓存策略：
   - 秒杀活动信息可以缓存到Redis
   - 秒杀商品库存使用Redis计数器
   - 用户购买限制使用Redis记录

3. 并发控制：
   - 使用Redis分布式锁防止超卖
   - 数据库层面使用乐观锁更新库存
   - 限制单用户请求频率

4. 监控指标：
   - 监控秒杀成功率
   - 监控数据库连接数和查询性能
   - 监控Redis内存使用情况
*/

-- 执行完成提示
SELECT '秒杀系统表结构创建完成！' as message, NOW() as createTime;



-- ========================================
-- 检查和添加基础数据
-- ========================================

-- ========================================
-- 书店系统示例数据插入脚本
-- 注意：请先执行 bookstore_ddl.sql 创建表结构
-- ========================================
#
# USE `bookstore`;
#
# -- ========================================
# -- 用户数据
# -- ========================================
#
# -- 插入管理员用户
# INSERT INTO `user` (`account`, `password`, `name`, `gender`, `manage`, `enable`) VALUES
#                                                                                      ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '管理员', '男', 1, 1),
#                                                                                      ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '张三', '男', 0, 1),
#                                                                                      ('<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '李四', '女', 0, 1);
#
# -- 插入地址数据
# INSERT INTO `address` (`account`, `name`, `phone`, `addr`, `label`) VALUES
#                                                                         ('<EMAIL>', '张三', '***********', '北京市朝阳区某某街道123号', '家'),
#                                                                         ('<EMAIL>', '张三', '***********', '北京市海淀区某某大厦456号', '公司'),
#                                                                         ('<EMAIL>', '李四', '***********', '上海市浦东新区某某路789号', '家');
#
# -- ========================================
# -- 图书分类数据
# -- ========================================
#
# -- 一级分类
# INSERT INTO `booksort` (`sortName`, `upperName`, `level`, `rank`) VALUES
#                                                                       ('文学', '无', '级别一', 1),
#                                                                       ('科技', '无', '级别一', 2),
#                                                                       ('教育', '无', '级别一', 3),
#                                                                       ('生活', '无', '级别一', 4);
#
# -- 二级分类
# INSERT INTO `booksort` (`sortName`, `upperName`, `level`, `rank`) VALUES
#                                                                       ('小说', '文学', '级别二', 1),
#                                                                       ('散文', '文学', '级别二', 2),
#                                                                       ('诗歌', '文学', '级别二', 3),
#                                                                       ('计算机', '科技', '级别二', 1),
#                                                                       ('电子', '科技', '级别二', 2),
#                                                                       ('教材', '教育', '级别二', 1),
#                                                                       ('考试', '教育', '级别二', 2),
#                                                                       ('健康', '生活', '级别二', 1),
#                                                                       ('美食', '生活', '级别二', 2);
#
# -- ========================================
# -- 出版社数据
# -- ========================================
#
# INSERT INTO `publish` (`name`, `showPublish`, `rank`) VALUES
#                                                           ('人民文学出版社', 1, 1),
#                                                           ('机械工业出版社', 1, 2),
#                                                           ('清华大学出版社', 1, 3),
#                                                           ('电子工业出版社', 1, 4),
#                                                           ('中信出版社', 1, 5);
#
# -- ========================================
# -- 图书数据
# -- ========================================
#
# INSERT INTO `book` (`bookName`, `author`, `isbn`, `publish`, `birthday`, `marketPrice`, `price`, `stock`, `description`, `put`, `rank`, `newProduct`, `recommend`) VALUES
#                                                                                                                                                                        ('红楼梦', '曹雪芹', '9787020002207', '人民文学出版社', '2020-01-01 00:00:00', 59.00, 45.00, 100, '中国古典文学四大名著之一', 1, 10, 0, 1),
#                                                                                                                                                                        ('Java核心技术', 'Cay S. Horstmann', '9787111213826', '机械工业出版社', '2021-03-15 00:00:00', 128.00, 98.00, 50, 'Java编程经典教程', 1, 9, 1, 1),
#                                                                                                                                                                        ('算法导论', 'Thomas H. Cormen', '9787111407010', '机械工业出版社', '2020-06-01 00:00:00', 158.00, 128.00, 30, '计算机算法经典教材', 1, 8, 0, 1),
#                                                                                                                                                                        ('Spring Boot实战', '汪云飞', '9787121291005', '电子工业出版社', '2021-05-20 00:00:00', 89.00, 69.00, 80, 'Spring Boot开发实战指南', 1, 7, 1, 0),
#                                                                                                                                                                        ('西游记', '吴承恩', '9787020002214', '人民文学出版社', '2020-02-01 00:00:00', 49.00, 38.00, 120, '中国古典文学四大名著之一', 1, 6, 0, 1);
#
# -- ========================================
# -- 图书图片数据
# -- ========================================
#
# INSERT INTO `bookimg` (`isbn`, `imgSrc`, `cover`) VALUES
#                                                       ('9787020002207', 'static/image/book/hongloumeng_cover.jpg', 1),
#                                                       ('9787020002207', 'static/image/book/hongloumeng_1.jpg', 0),
#                                                       ('9787111213826', 'static/image/book/java_cover.jpg', 1),
#                                                       ('9787111213826', 'static/image/book/java_1.jpg', 0),
#                                                       ('9787111407010', 'static/image/book/algorithm_cover.jpg', 1),
#                                                       ('9787121291005', 'static/image/book/springboot_cover.jpg', 1),
#                                                       ('9787020002214', 'static/image/book/xiyouji_cover.jpg', 1);
#
# -- ========================================
# -- 图书分类关联数据
# -- ========================================
#
# INSERT INTO `booksortlist` (`bookSortId`, `bookId`) VALUES
#                                                         (5, 1),  -- 红楼梦 -> 小说
#                                                         (5, 5),  -- 西游记 -> 小说
#                                                         (8, 2),  -- Java核心技术 -> 计算机
#                                                         (8, 3),  -- 算法导论 -> 计算机
#                                                         (8, 4);  -- Spring Boot实战 -> 计算机
#
# -- ========================================
# -- 书单数据
# -- ========================================
#
# INSERT INTO `booktopic` (`topicName`, `subTitle`, `cover`, `rank`, `put`) VALUES
#                                                                               ('程序员必读书单', '提升编程技能的经典书籍', 'static/image/topic/programmer_books.jpg', 1, 1),
#                                                                               ('古典文学精选', '传承千年的文学瑰宝', 'static/image/topic/classic_literature.jpg', 2, 1);
#
# INSERT INTO `subbooktopic` (`topicId`, `bookId`, `recommendReason`) VALUES
#                                                                         (1, 2, 'Java开发者的必备参考书，内容全面深入'),
#                                                                         (1, 3, '算法学习的经典教材，计算机科学基础'),
#                                                                         (1, 4, '现代Java开发框架实战指南'),
#                                                                         (2, 1, '中国古典小说的巅峰之作，文学价值极高'),
#                                                                         (2, 5, '神话色彩浓厚的古典小说，想象力丰富');
#
# -- ========================================
# -- 购物车示例数据
# -- ========================================
#
# INSERT INTO `cart` (`account`, `id`, `num`) VALUES
#                                                 ('<EMAIL>', 1, 2),
#                                                 ('<EMAIL>', 2, 1),
#                                                 ('<EMAIL>', 3, 1);
#
# -- ========================================
# -- 订单示例数据
# -- ========================================
#
# INSERT INTO `bookorder` (`orderId`, `account`, `addressId`, `orderTime`, `orderStatus`) VALUES
#                                                                                             ('ORD202401010001', '<EMAIL>', 1, '2024-01-01 10:30:00', '已完成'),
#                                                                                             ('ORD202401020001', '<EMAIL>', 3, '2024-01-02 14:20:00', '待发货');
#
# INSERT INTO `orderdetail` (`orderId`, `bookId`, `num`, `price`) VALUES
#                                                                     ('ORD202401010001', 1, 1, 45.00),
#                                                                     ('ORD202401010001', 2, 1, 98.00),
#                                                                     ('ORD202401020001', 3, 1, 128.00);
#
# INSERT INTO `expense` (`orderId`, `productTotalMoney`, `freight`, `coupon`, `activityDiscount`, `allPrice`, `finallyPrice`) VALUES
#                                                                                                                                 ('ORD202401010001', 143.00, 0.00, 0, 0.00, 143.00, 143.00),
#                                                                                                                                 ('ORD202401020001', 128.00, 0.00, 0, 0.00, 128.00, 128.00);
#
# -- ========================================
# -- 更新出版社图书数量
# -- ========================================
#
# UPDATE `publish` SET `num` = (
#     SELECT COUNT(*) FROM `book` WHERE `book`.`publish` = `publish`.`name`
# ) WHERE `id` > 0;
#
#

-- ========================================
-- 公告与网站介绍相关表
-- ========================================

-- 公告表
CREATE TABLE `announcement` (
                                `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '公告编号',
                                `title` VARCHAR(255) NOT NULL COMMENT '公告标题',
                                `content` TEXT NOT NULL COMMENT '公告内容',
                                `author` VARCHAR(100) DEFAULT NULL COMMENT '发布人账号',
                                `publishTime` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
                                `enable` TINYINT(1) DEFAULT '1' COMMENT '是否展示 (1=展示,0=隐藏)',
                                PRIMARY KEY (`id`),
                                KEY `idx_enable` (`enable`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站公告表';

-- 网站介绍表 (理论上只保存一条记录)
CREATE TABLE `about` (
                         `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID (固定为1)',
                         `content` TEXT NOT NULL COMMENT '网站介绍内容',
                         `updateTime` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
                         PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='网站介绍信息表';


-- ========================================
-- 秒杀系统数据库更新脚本 v1.0
-- 适用于现有bookstore系统的秒杀功能升级
-- 执行前请确保已连接到bookstore数据库
-- ========================================

USE `bookstore`;

-- ========================================
-- 1. 检查并创建秒杀活动表
-- ========================================
CREATE TABLE IF NOT EXISTS `spikeactivity` (
                                               `id` bigint NOT NULL AUTO_INCREMENT COMMENT '活动ID',
                                               `activityName` varchar(100) NOT NULL COMMENT '活动名称',
                                               `activityDesc` text COMMENT '活动描述',
                                               `startTime` datetime NOT NULL COMMENT '开始时间',
                                               `endTime` datetime NOT NULL COMMENT '结束时间',
                                               `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始，1-进行中，2-已结束，3-已取消',
                                               `createTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                               `updateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                               `createdBy` varchar(100) DEFAULT NULL COMMENT '创建人账号',
                                               PRIMARY KEY (`id`),
                                               KEY `idx_startEndTime` (`startTime`,`endTime`),
                                               KEY `idx_status` (`status`),
                                               KEY `idx_createdBy` (`createdBy`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='秒杀活动表';

-- ========================================
-- 2. 检查并创建秒杀商品表
-- ========================================
CREATE TABLE IF NOT EXISTS `spikegoods` (
                                            `id` bigint NOT NULL AUTO_INCREMENT COMMENT '秒杀商品ID',
                                            `activityId` bigint NOT NULL COMMENT '活动ID',
                                            `bookId` int NOT NULL COMMENT '图书ID',
                                            `spikePrice` decimal(10,2) NOT NULL COMMENT '秒杀价格',
                                            `originalPrice` decimal(10,2) NOT NULL COMMENT '原价',
                                            `spikeStock` int NOT NULL COMMENT '秒杀库存',
                                            `soldCount` int DEFAULT '0' COMMENT '已售数量',
                                            `limitPerUser` int DEFAULT '1' COMMENT '每人限购数量',
                                            `sortOrder` int DEFAULT '0' COMMENT '排序',
                                            `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-下架，1-上架',
                                            `createTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                            `updateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                            PRIMARY KEY (`id`),
                                            KEY `idx_activityId` (`activityId`),
                                            KEY `idx_bookId` (`bookId`),
                                            KEY `idx_status` (`status`),
                                            KEY `idx_sortOrder` (`sortOrder`),
                                            CONSTRAINT `fk_spikeGoods_activity` FOREIGN KEY (`activityId`) REFERENCES `spikeactivity` (`id`) ON DELETE CASCADE,
                                            CONSTRAINT `fk_spikeGoods_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='秒杀商品表';

-- ========================================
-- 3. 检查并创建秒杀记录表（用于限购检查和行为分析）
-- ========================================
CREATE TABLE IF NOT EXISTS `spikerecord` (
                                             `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
                                             `spikeGoodsId` bigint NOT NULL COMMENT '秒杀商品ID',
                                             `userAccount` varchar(100) NOT NULL COMMENT '用户账号',
                                             `quantity` int NOT NULL DEFAULT '1' COMMENT '购买数量',
                                             `spikeTime` datetime NOT NULL COMMENT '秒杀时间',
                                             `result` tinyint(1) NOT NULL COMMENT '结果：0-失败，1-成功',
                                             `failReason` varchar(200) DEFAULT NULL COMMENT '失败原因',
                                             `ipAddress` varchar(45) DEFAULT NULL COMMENT 'IP地址',
                                             `userAgent` varchar(500) DEFAULT NULL COMMENT '用户代理',
                                             PRIMARY KEY (`id`),
                                             KEY `idx_spikeGoodsUser` (`spikeGoodsId`,`userAccount`),
                                             KEY `idx_spikeTime` (`spikeTime`),
                                             KEY `idx_result` (`result`),
                                             KEY `idx_userAccount` (`userAccount`),
                                             CONSTRAINT `fk_spikeRecord_goods` FOREIGN KEY (`spikeGoodsId`) REFERENCES `spikegoods` (`id`) ON DELETE CASCADE,
                                             CONSTRAINT `fk_spikeRecord_user` FOREIGN KEY (`userAccount`) REFERENCES `user` (`account`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='秒杀记录表';

-- ========================================
-- 4. 数据库结构升级检查
-- ========================================

-- 检查spikerecord表是否有quantity字段，如果没有则添加
SET @column_exists = (
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'bookstore'
      AND TABLE_NAME = 'spikerecord'
      AND COLUMN_NAME = 'quantity'
);

SET @sql = IF(@column_exists = 0,
              'ALTER TABLE spikerecord ADD COLUMN quantity int NOT NULL DEFAULT 1 COMMENT ''购买数量'' AFTER userAccount;',
              'SELECT ''quantity column already exists'' as message;'
           );

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ========================================
-- 5. 创建性能优化索引
-- ========================================

-- 秒杀活动表复合索引
CREATE INDEX `idx_activity_time_status` ON `spikeactivity` (`startTime`, `endTime`, `status`);

-- 秒杀商品表复合索引
CREATE INDEX `idx_goods_activity_status` ON `spikegoods` (`activityId`, `status`);
CREATE INDEX `idx_goods_book_status` ON `spikegoods` (`bookId`, `status`);

-- 秒杀记录表复合索引（用于限购检查优化）
CREATE INDEX `idx_record_user_goods_result` ON `spikerecord` (`userAccount`, `spikeGoodsId`, `result`);

-- ========================================
-- 6. 插入示例数据（仅在表为空时插入）
-- ========================================

-- 插入秒杀活动示例数据
INSERT IGNORE INTO `spikeactivity` (`id`, `activityName`, `activityDesc`, `startTime`, `endTime`, `status`, `createdBy`) VALUES
                                                                                                                             (1, '新年特惠秒杀', '新年期间限时秒杀活动，精选图书超低价', '2024-01-01 10:00:00', '2024-01-01 12:00:00', 1, '<EMAIL>'),
                                                                                                                             (2, '午间秒杀场', '午间休息时间秒杀专场', '2024-01-01 12:00:00', '2024-01-01 14:00:00', 0, '<EMAIL>'),
                                                                                                                             (3, '晚间秒杀场', '晚间黄金时段秒杀活动', '2024-01-01 20:00:00', '2024-01-01 22:00:00', 0, '<EMAIL>');

-- 插入秒杀商品示例数据（基于现有的图书数据）
INSERT IGNORE INTO `spikegoods` (`id`, `activityId`, `bookId`, `spikePrice`, `originalPrice`, `spikeStock`, `limitPerUser`, `sortOrder`) VALUES
                                                                                                                                             (1, 1, 1, 19.90, 45.00, 50, 2, 1),  -- 红楼梦
                                                                                                                                             (2, 1, 2, 49.90, 98.00, 30, 1, 2),  -- Java核心技术
                                                                                                                                             (3, 1, 3, 69.90, 128.00, 20, 1, 3), -- 算法导论
                                                                                                                                             (4, 2, 4, 39.90, 69.00, 40, 2, 1),  -- Spring Boot实战
                                                                                                                                             (5, 2, 5, 15.90, 38.00, 60, 2, 2),  -- 西游记
                                                                                                                                             (6, 3, 1, 22.90, 45.00, 30, 1, 1),  -- 红楼梦（晚场）
                                                                                                                                             (7, 3, 2, 59.90, 98.00, 25, 1, 2);  -- Java核心技术（晚场）

-- ========================================
-- 7. 清理工作
-- ========================================

-- 删除旧的spikeorder备份表（数据已迁移到spikerecord表）
DROP TABLE IF EXISTS `spikeorder_backup`;

-- ========================================
-- 8. 验证安装
-- ========================================

-- 显示创建的表
SELECT 'Tables created successfully:' as message;
SHOW TABLES LIKE '%spike%';

-- 显示表记录数
SELECT
    'spikeactivity' as table_name,
    COUNT(*) as record_count
FROM spikeactivity
UNION ALL
SELECT
    'spikegoods' as table_name,
    COUNT(*) as record_count
FROM spikegoods
UNION ALL
SELECT
    'spikerecord' as table_name,
    COUNT(*) as record_count
FROM spikerecord;

-- ========================================
-- 安装完成提示
-- ========================================
SELECT '========================================' as message
UNION ALL
SELECT '秒杀系统数据库更新完成！' as message
UNION ALL
SELECT '版本：v1.0' as message
UNION ALL
SELECT '更新内容：' as message
UNION ALL
SELECT '1. 创建/更新秒杀活动表(spikeactivity)' as message
UNION ALL
SELECT '2. 创建/更新秒杀商品表(spikegoods)' as message
UNION ALL
SELECT '3. 创建/更新秒杀记录表(spikerecord)' as message
UNION ALL
SELECT '4. 添加quantity字段用于限购检查' as message
UNION ALL
SELECT '5. 优化索引提升查询性能' as message
UNION ALL
SELECT '6. 插入示例数据供测试使用' as message
UNION ALL
SELECT '========================================' as message;


-- ========================================
-- 书店系统优惠券扩展SQL脚本
-- 支持满减券和折扣券两种类型
-- 管理员可创建发放，用户可领取使用
--
-- 使用说明：
-- 1. 此脚本适用于已有bookstore数据库的系统
-- 2. 会在现有系统基础上添加优惠券功能
-- 3. 如果表已存在会报错，这是正常的，可以忽略
-- ========================================

-- 1. 优惠券模板表（管理员创建的优惠券类型）
CREATE TABLE `coupon_template` (
                                   `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '优惠券模板ID',
                                   `name` varchar(100) NOT NULL COMMENT '优惠券名称',
                                   `type` tinyint(1) NOT NULL COMMENT '优惠券类型：1-满减券，2-折扣券',
                                   `discount_value` decimal(10,2) NOT NULL COMMENT '折扣值（满减券为减免金额，折扣券为折扣百分比，如85表示8.5折）',
                                   `min_order_amount` decimal(10,2) DEFAULT '0.00' COMMENT '最低消费金额',
                                   `max_discount_amount` decimal(10,2) DEFAULT NULL COMMENT '最大折扣金额（仅折扣券使用）',
                                   `total_quantity` int(11) NOT NULL COMMENT '发放总数量',
                                   `used_quantity` int(11) DEFAULT '0' COMMENT '已使用数量',
                                   `received_quantity` int(11) DEFAULT '0' COMMENT '已领取数量',
                                   `per_user_limit` int(11) DEFAULT '1' COMMENT '每用户限领数量',
                                   `valid_days` int(11) NOT NULL COMMENT '有效天数（从领取日开始计算）',
                                   `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-停用，1-启用',
                                   `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                   PRIMARY KEY (`id`),
                                   KEY `idx_type` (`type`),
                                   KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='优惠券模板表';

-- 2. 用户优惠券表（用户领取的优惠券实例）
CREATE TABLE `user_coupon` (
                               `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户优惠券ID',
                               `coupon_template_id` int(11) NOT NULL COMMENT '优惠券模板ID',
                               `account` varchar(100) NOT NULL COMMENT '用户账号',
                               `coupon_code` varchar(50) NOT NULL COMMENT '优惠券码（自动生成）',
                               `status` tinyint(2) DEFAULT '1' COMMENT '状态：1-未使用，2-已使用，3-已过期',
                               `receive_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '领取时间',
                               `use_time` timestamp NULL DEFAULT NULL COMMENT '使用时间',
                               `order_id` varchar(50) DEFAULT NULL COMMENT '使用的订单号',
                               `expire_time` timestamp NOT NULL COMMENT '过期时间',
                               `discount_amount` decimal(10,2) DEFAULT '0.00' COMMENT '实际折扣金额（使用时计算并存储）',
                               `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                               `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                               PRIMARY KEY (`id`),
                               UNIQUE KEY `uk_coupon_code` (`coupon_code`),
                               KEY `idx_account` (`account`),
                               KEY `idx_template_id` (`coupon_template_id`),
                               KEY `idx_status` (`status`),
                               KEY `idx_expire_time` (`expire_time`),
                               KEY `idx_account_status` (`account`, `status`),
                               CONSTRAINT `fk_user_coupon_template` FOREIGN KEY (`coupon_template_id`) REFERENCES `coupon_template` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户优惠券表';

-- ========================================
-- 修改现有表结构
-- ========================================

-- 3. 修改expense表，增加优惠券相关字段
ALTER TABLE `expense`
    ADD COLUMN `coupon_id` int(11) DEFAULT NULL COMMENT '使用的优惠券ID' AFTER `coupon`,
    ADD COLUMN `coupon_discount` decimal(10,2) DEFAULT '0.00' COMMENT '优惠券折扣金额' AFTER `coupon_id`,
    ADD INDEX `idx_coupon_id` (`coupon_id`);

-- 4. 修改bookorder表，增加优惠券字段
ALTER TABLE `bookorder`
    ADD COLUMN `coupon_id` int(11) DEFAULT NULL COMMENT '使用的优惠券ID' AFTER `beUserDelete`;

-- ========================================
-- 插入示例数据
-- ========================================

-- 插入优惠券模板示例数据
INSERT INTO `coupon_template` (`name`, `type`, `discount_value`, `min_order_amount`, `max_discount_amount`, `total_quantity`, `per_user_limit`, `valid_days`) VALUES
                                                                                                                                                                  ('新用户专享券', 1, 20.00, 100.00, NULL, 1000, 1, 30),
                                                                                                                                                                  ('满200减50券', 1, 50.00, 200.00, NULL, 500, 2, 15),
                                                                                                                                                                  ('图书节8.5折券', 2, 85.00, 50.00, 50.00, 200, 1, 7),
                                                                                                                                                                  ('会员9折券', 2, 90.00, 100.00, 30.00, 300, 3, 30);

-- 插入用户优惠券示例数据（为已有用户分配优惠券）
INSERT INTO `user_coupon` (`coupon_template_id`, `account`, `coupon_code`, `expire_time`) VALUES
                                                                                              (2, '<EMAIL>', 'FULL20241201002000001', DATE_ADD(NOW(), INTERVAL 15 DAY)),
                                                                                              (1, '<EMAIL>', 'FULL20241201001000002', DATE_ADD(NOW(), INTERVAL 30 DAY)),
                                                                                              (3, '<EMAIL>', 'DISC20241201003000001', DATE_ADD(NOW(), INTERVAL 7 DAY));

-- ========================================
-- 创建视图（方便查询）
-- ========================================

-- 创建用户可用优惠券视图
CREATE VIEW `user_available_coupons` AS
SELECT
    uc.id as user_coupon_id,
    uc.account,
    uc.coupon_code,
    ct.name as coupon_name,
    ct.type,
    ct.discount_value,
    ct.min_order_amount,
    ct.max_discount_amount,
    uc.receive_time,
    uc.expire_time,
    CASE
        WHEN ct.type = 1 THEN CONCAT('满', ct.min_order_amount, '减', ct.discount_value)
        WHEN ct.type = 2 THEN CONCAT(ct.discount_value/10, '折优惠')
        END as discount_desc
FROM user_coupon uc
         JOIN coupon_template ct ON uc.coupon_template_id = ct.id
WHERE uc.status = 1
  AND uc.expire_time > NOW()
  AND ct.status = 1;


-- 插入推荐图书数据
-- 这个脚本用于为推荐图书区域添加测试数据

USE `bookstore`;

-- 首先检查是否有图书数据
SELECT COUNT(*) as book_count FROM book;

-- 清空现有的推荐数据（如果需要重新开始）
-- DELETE FROM recommend;
-- DELETE FROM newproduct;

-- 插入推荐图书数据
-- 选择前10本已上架的图书作为推荐图书
INSERT IGNORE INTO `recommend` (`bookId`, `rank`, `addTime`)
SELECT `id`, 1, NOW() FROM `book`
WHERE `put` = 1
ORDER BY `id`
LIMIT 10;

-- 插入新品推荐数据
-- 选择前10本已上架的图书作为新品推荐
INSERT IGNORE INTO `newproduct` (`bookId`, `rank`, `addTime`)
SELECT `id`, 1, NOW() FROM `book`
WHERE `put` = 1
ORDER BY `id` DESC
LIMIT 10;

-- 更新book表中的推荐和新品标志
UPDATE `book` SET `recommend` = 1
WHERE `id` IN (SELECT `bookId` FROM `recommend`);

UPDATE `book` SET `newProduct` = 1
WHERE `id` IN (SELECT `bookId` FROM `newproduct`);

-- 查看插入结果
SELECT 'recommend表数据:' as info;
SELECT r.id, r.bookId, b.bookName, r.rank, r.addTime
FROM recommend r
         JOIN book b ON r.bookId = b.id
ORDER BY r.rank DESC, r.addTime DESC;

SELECT 'newproduct表数据:' as info;
SELECT n.id, n.bookId, b.bookName, n.rank, n.addTime
FROM newproduct n
         JOIN book b ON n.bookId = b.id
ORDER BY n.rank DESC, n.addTime DESC;

-- 检查book表中的推荐标志
SELECT 'book表中的推荐图书:' as info;
SELECT id, bookName, author, recommend, newProduct, put
FROM book
WHERE recommend = 1 OR newProduct = 1
ORDER BY id;


-- ========================================
-- 图书评价表
-- ========================================
CREATE TABLE `book_comment` (
                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论编号',
                                `bookId` int(11) NOT NULL COMMENT '图书ID',
                                `userId` int(11) NOT NULL COMMENT '用户ID',
                                `parentId` int(11) DEFAULT NULL COMMENT '父评论ID（用于二级评论）',
                                `content` text NOT NULL COMMENT '评论内容',
                                `likeCount` int(11) DEFAULT '0' COMMENT '点赞数',
                                `createTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                PRIMARY KEY (`id`),
                                KEY `idx_bookId` (`bookId`),
                                KEY `idx_userId` (`userId`),
                                KEY `idx_parentId` (`parentId`),
                                KEY `idx_createTime` (`createTime`),
                                KEY `idx_likeCount` (`likeCount`),
                                CONSTRAINT `fk_comment_book` FOREIGN KEY (`bookId`) REFERENCES `book` (`id`) ON DELETE CASCADE,
                                CONSTRAINT `fk_comment_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE,
                                CONSTRAINT `fk_comment_parent` FOREIGN KEY (`parentId`) REFERENCES `book_comment` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='图书评价表';

-- ========================================
-- 评论点赞记录表（可选，用于记录用户点赞历史）
-- ========================================
CREATE TABLE `comment_like` (
                                `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '点赞记录编号',
                                `commentId` int(11) NOT NULL COMMENT '评论ID',
                                `userId` int(11) NOT NULL COMMENT '用户ID',
                                PRIMARY KEY (`id`),
                                UNIQUE KEY `uk_comment_user` (`commentId`, `userId`),
                                KEY `idx_userId` (`userId`),
                                CONSTRAINT `fk_like_comment` FOREIGN KEY (`commentId`) REFERENCES `book_comment` (`id`) ON DELETE CASCADE,
                                CONSTRAINT `fk_like_user` FOREIGN KEY (`userId`) REFERENCES `user` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论点赞记录表';

-- ========================================
-- 插入示例数据
-- ========================================

-- 插入图书评论示例数据（一级评论）
INSERT INTO `book_comment` (`bookId`, `userId`, `content`, `likeCount`, `createTime`) VALUES
                                                                                          (1, 2, '这本书写得非常好，内容详实，值得推荐！', 15, '2024-01-01 10:30:00'),
                                                                                          (1, 3, '红楼梦不愧是四大名著之一，文笔优美，情节引人入胜。', 23, '2024-01-02 14:20:00'),
                                                                                          (2, 2, 'Java核心技术这本书对初学者很友好，讲解得很清楚。', 18, '2024-01-03 09:15:00'),
                                                                                          (2, 3, '作为Java开发者，这本书是必读的经典教材。', 31, '2024-01-04 16:45:00'),
                                                                                          (3, 2, '算法导论内容很全面，但有些章节比较难理解。', 12, '2024-01-05 11:30:00'),
                                                                                          (3, 3, '这本书是算法学习的权威教材，值得反复阅读。', 27, '2024-01-06 13:20:00'),
                                                                                          (4, 2, 'Spring Boot实战很实用，案例丰富，适合项目开发。', 19, '2024-01-07 15:10:00'),
                                                                                          (5, 3, '西游记的故事很有趣，适合各个年龄段的读者。', 25, '2024-01-08 10:25:00');

-- 插入二级评论示例数据（回复评论）
INSERT INTO `book_comment` (`bookId`, `userId`, `parentId`, `content`, `likeCount`, `createTime`) VALUES
                                                                                                      (1, 3, 1, '同意你的观点，这本书确实很棒！', 8, '2024-01-01 11:00:00'),
                                                                                                      (1, 2, 1, '谢谢推荐，我也觉得很有收获。', 5, '2024-01-01 12:30:00'),
                                                                                                      (1, 2, 2, '曹雪芹的文笔确实很厉害，每个角色都刻画得很生动。', 12, '2024-01-02 15:00:00'),
                                                                                                      (2, 3, 3, '对，这本书很适合入门学习。', 6, '2024-01-03 10:00:00'),
                                                                                                      (2, 2, 4, '确实，这本书在Java圈子里口碑很好。', 9, '2024-01-04 17:00:00'),
                                                                                                      (3, 3, 5, '建议先看基础章节，再深入复杂算法。', 7, '2024-01-05 12:00:00'),
                                                                                                      (3, 2, 6, '同意，经典教材值得收藏。', 4, '2024-01-06 14:00:00'),
                                                                                                      (4, 3, 7, 'Spring Boot确实让开发变得更简单了。', 11, '2024-01-07 16:00:00'),
                                                                                                      (5, 2, 8, '西游记的想象力真的很丰富！', 8, '2024-01-08 11:00:00');

-- 插入评论点赞记录示例数据
INSERT INTO `comment_like` (`commentId`, `userId`) VALUES
                                                       (1, 3),
                                                       (1, 2),
                                                       (2, 2),
                                                       (2, 3),
                                                       (3, 3),
                                                       (3, 2),
                                                       (4, 2),
                                                       (4, 3),
                                                       (5, 3),
                                                       (6, 2),
                                                       (7, 3),
                                                       (8, 2),
                                                       (9, 2),
                                                       (10, 3),
                                                       (11, 2),
                                                       (12, 3),
                                                       (13, 2),
                                                       (14, 3),
                                                       (15, 2),
                                                       (16, 3),
                                                       (17, 2);

-- ========================================
-- 验证数据插入结果
-- ========================================

-- 查看评论数据统计
SELECT
    'book_comment' as table_name,
    COUNT(*) as total_comments,
    COUNT(CASE WHEN parentId IS NULL THEN 1 END) as first_level_comments,
    COUNT(CASE WHEN parentId IS NOT NULL THEN 1 END) as second_level_comments,
    SUM(likeCount) as total_likes
FROM book_comment;

-- 查看点赞记录统计
SELECT
    'comment_like' as table_name,
    COUNT(*) as total_likes,
    COUNT(DISTINCT commentId) as commented_articles,
    COUNT(DISTINCT userId) as active_users
FROM comment_like;

-- 查看热门评论（按点赞数排序）
SELECT
    c.id,
    c.content,
    c.likeCount,
    c.createTime,
    b.bookName,
    u.name as userName
FROM book_comment c
         JOIN book b ON c.bookId = b.id
         JOIN user u ON c.userId = u.id
WHERE c.parentId IS NULL
ORDER BY c.likeCount DESC
LIMIT 5;

-- 查看评论层级结构
SELECT
    c1.id as comment_id,
    c1.content as comment_content,
    c1.likeCount,
    c2.id as reply_id,
    c2.content as reply_content,
    c2.likeCount as reply_likes
FROM book_comment c1
         LEFT JOIN book_comment c2 ON c1.id = c2.parentId
WHERE c1.parentId IS NULL
ORDER BY c1.createTime DESC, c2.createTime ASC
LIMIT 10;