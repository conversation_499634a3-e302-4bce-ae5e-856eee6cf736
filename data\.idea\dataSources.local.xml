<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="DB-251.26094.87">
    <data-source name="0@localhost" uuid="15bff07b-47ee-458d-9b52-0152715439db">
      <database-info product="Redis Standalone" version="********" jdbc-version="4.2" driver-name="Redis JDBC Driver" driver-version="1.5" dbms="REDIS" exact-version="********" exact-driver-version="1.5" />
      <case-sensitivity plain-identifiers="mixed" quoted-identifiers="mixed" />
      <secret-storage>master_key</secret-storage>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema" qname="@" />
        </introspection-scope>
      </schema-mapping>
    </data-source>
    <data-source name="bookstore@localhost" uuid="beaa25b5-7c0e-4e96-b946-81c3f41f4af3">
      <database-info product="MySQL" version="8.4.4" jdbc-version="4.2" driver-name="MySQL Connector/J" driver-version="mysql-connector-j-8.2.0 (Revision: 06a1f724497fd81c6a659131fda822c9e5085b6c)" dbms="MYSQL" exact-version="8.4.4" exact-driver-version="8.2">
        <extra-name-characters>#@</extra-name-characters>
        <identifier-quote-string>`</identifier-quote-string>
        <jdbc-catalog-is-schema>true</jdbc-catalog-is-schema>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="lower" />
      <secret-storage>master_key</secret-storage>
      <user-name>root</user-name>
      <schema-mapping>
        <introspection-scope>
          <node kind="schema">
            <name qname="bookstore" />
            <name qname="@" />
          </node>
        </introspection-scope>
      </schema-mapping>
      <load-sources>user_and_system_sources</load-sources>
    </data-source>
  </component>
</project>